'use client';

import { ICustomerGroups } from '@/apis/customer-groups/customer-groups.type';
import CollapseApp from '@/components/common/CollapseApp';
import FormController from '@/components/common/FormController';
import { yupResolver } from '@hookform/resolvers/yup';
import { FormProvider, useForm } from 'react-hook-form';
import { Button, Card, Col, FormGroup, Label, Row } from 'reactstrap';
import * as yup from 'yup';
import ListCustomer from './ListCustomer';

import Avatar from '@/components/common/Avatar';

// Schema validation cho CustomerGroups
const customerGroupsSchema = yup.object().shape({
    name: yup
        .string()
        .required('Tên nhóm khách hàng là trường bắt buộc')
        .min(2, 'Tên nhóm khách hàng phải có ít nhất 2 ký tự')
        .max(100, 'Tên nhóm khách hàng không được quá 100 ký tự')
        .trim('Tên nhóm khách hàng không được có khoảng trắng ở đầu và cuối'),
    description: yup
        .string()
        .max(500, 'Mô tả không được quá 500 ký tự')
        .nullable()
        .transform((value) => value || ''),
});

type CustomerGroupsFormData = yup.InferType<typeof customerGroupsSchema>;

interface CustomerGroupsFormProps {
    initValue?: ICustomerGroups;
    onSubmit?: (data: ICustomerGroups) => void;
    onEdit?: (data: ICustomerGroups) => void;
    onCancel: () => void;
    mode?: 'create' | 'edit';
}

const CustomerGroupsForm = ({
    initValue,
    onSubmit,
    // onEdit,
    onCancel,
    mode = 'create',
}: CustomerGroupsFormProps) => {
    const handleFormSubmit = (data: CustomerGroupsFormData) => {
        if (onSubmit) {
            onSubmit(data as unknown as ICustomerGroups);
        }
    };

    const methods = useForm<CustomerGroupsFormData>({
        defaultValues: {
            name: initValue?.name || '',
            description: initValue?.description || '',
        },
        resolver: yupResolver(customerGroupsSchema),
        mode: 'onChange',
    });

    return (
        <FormProvider {...methods}>
            <Card style={{ padding: '20px 40px 20px 40px' }}>
                <CollapseApp title='THÔNG TIN CHÍNH'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <FormController
                                controlType='textInput'
                                name='name'
                                label='Tên nhóm khách hàng'
                                placeholder='Nhập tên nhóm khách hàng'
                                required={true}
                            />
                        </Col>
                        <Col md='11'>
                            <FormController
                                controlType='textarea'
                                name='description'
                                label='Mô tả'
                                placeholder='Nhập mô tả nhóm khách hàng'
                            />
                        </Col>
                        <Col md='11'>
                            <FormGroup>
                                <Label>
                                    <strong>Ảnh đại diện</strong>
                                </Label>
                                <div style={{ height: '50px' }}>
                                    <Avatar />
                                </div>
                            </FormGroup>
                        </Col>
                    </Row>
                </CollapseApp>
                <CollapseApp title='THÔNG TIN LIÊN KẾT'>
                    <Row className='g-3 justify-content-around'>
                        <Col md='11'>
                            <ListCustomer title='Khách hàng' />
                        </Col>
                    </Row>
                </CollapseApp>

                <Row className='g-3 justify-content-around'>
                    <Col md='11' className='mt-4 d-flex justify-content-end'>
                        <Button
                            color='danger'
                            className='me-2'
                            type='button'
                            onClick={onCancel}
                        >
                            Hủy
                        </Button>
                        {mode === 'create' && (
                            <Button
                                color='success'
                                onClick={methods.handleSubmit(handleFormSubmit)}
                            >
                                Tạo mới
                            </Button>
                        )}
                        {mode === 'edit' && (
                            <Button color='success'>Lưu</Button>
                        )}
                    </Col>
                </Row>
            </Card>
        </FormProvider>
    );
};

export default CustomerGroupsForm;
